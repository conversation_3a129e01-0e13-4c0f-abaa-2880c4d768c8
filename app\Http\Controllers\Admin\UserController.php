<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Requests\Admin\TempPasswordRequest;
use App\Http\Resources\DataTableCollection;
use App\Mail\ForgotPasswordLinkMail;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class UserController extends Controller
{
    public function index()
    {
        $page_title = 'Providers';

        return view('users.index', [
            'page_title' => $page_title
        ]);
    }

    public function create()
    {
        $has_back = request('return_url') ?: route('users.index');
        $page_title = 'Create Provider';
        $card_title = 'Provider';

        $user = app(User::class);
        $livewire_component = 'user.create-edit';
        $livewire_data = [
            'user' => $user,
            'page_title' => $page_title,
            'card_title' => $card_title
        ];
        return view('layouts.livewire', [
            'page_title' => $page_title,
            'card_title' => $card_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function edit(User $user)
    {
        dd('heee');
        $has_back = request('return_url') ?: route('users.index');
        $page_title = 'Edit Provider';
        $card_title = 'Provider';

        $livewire_component = 'user.create-edit';
        $livewire_data = [
            'user' => $user,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'card_title' => $card_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function delete(User $user)
    {
        // Log provider deletion before deleting
        LogService::logProviderDeleted($user);

        $user->delete();
        return response([
            'message' => __('messages.User_deleted'),
            'status' => '1',
        ]);
    }

    public function indexWeb(GetDatatableRequest $request)
    {
        $data = User::query()->where('role', '!=', User::ROLE_ADMIN)->where('role', '!=', User::ROLE_OPERATOR);
        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        if ($search) {
            $query_search = "%" . $search . "%";

            $data->where(function ($query) use ($query_search) {
                $query->where('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search);
            });
        }

        if ($sort_order && $sort_field) {
            $userColumns = Schema::getColumnListing((new User())->table);

            if (in_array($sort_field, $userColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection($data->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }

    public function pagination(Request $request)
    {
        $data = User::query();

        if ($request->has('search')) {
            $search = '%' . $request->search . '%';

            $data->where(function ($query) use ($search) {
                $query = $query->where('first_name', 'like', $search)
                    ->orWhere('last_name', 'like', $search);
            });
        }
        return $data->simplePaginate()->items();
    }

    public function change_status(User $user)
    {
        $user->is_active = !$user->is_active;
        $user->save();

        // Log the status change
        if ($user->is_active == 0) {
            LogService::logProviderActivated($user);
        } else {
            LogService::logProviderDeactivated($user);
        }

        return response([
            'message' => 'Updated Successfully',
            'status' => 1,
        ]);
    }

    public function sendTempPassword(TempPasswordRequest $request)
    {
        // Check if user exists without revealing this information to the client
        $user = User::where('email', $request->email)->first();

        // Only process password reset if user exists
        if ($user) {
            // Generate a new random password and ensure it's different from the current one
            $attempts = 0;
            $max_attempts = 5;
            $new_password = Str::random(8);

            // Check if the new password is the same as the old one (unlikely but possible)
            while (Hash::check($new_password, $user->password) && $attempts < $max_attempts) {
                $new_password = Str::random(11);
                $attempts++;
            }

            $user->password = bcrypt($new_password);
            $user->password_changed_at = null; // Force password change after reset
            $user->is_password_reset = true;
            $user->save();

            try {
                Mail::to($user->email)->send(new ForgotPasswordLinkMail($new_password, $user));

                // Log temporary password sent
                LogService::logTempPasswordSent($user);
            } catch (\Exception $e) {
                // Log the error
                Log::error('Failed to send temporary password email', [
                    'email' => $user->email,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Always return the same response regardless of whether the email exists
        // This is for security reasons to prevent email enumeration
        return redirect()->route('forgot-password', ['message_sent' => true]);
    }
}
